#!/usr/bin/env python3
"""
TNGD Backup Engine

Main backup orchestration engine that coordinates all backup operations
with improved resource management, error handling, and monitoring.

This module serves as the primary interface for backup operations,
integrating all core components into a cohesive system.
"""

import os
import sys
import time
import json
import logging
import gc
import tarfile
import calendar
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

# Import core components
from .config_manager import ConfigManager
from .devo_client import DevoClient
from .storage_manager import StorageManager
from .email_service import EmailService
from .streaming_processor import StreamingDataProcessor, StreamingConfig
# Note: Removed unused import of get_thread_manager

# Import utilities
from ..utils.monitoring import ResourceMonitor
from ..models.checkpoint import BackupCheckpoint
from ..models.backup_config import BackupConfig
from ..constants import BackupConstants

# Import performance optimization
try:
    from .performance_optimizer import PerformanceOptimizer
    PERFORMANCE_OPTIMIZATION_AVAILABLE = True
except ImportError:
    PERFORMANCE_OPTIMIZATION_AVAILABLE = False


class CheckpointHandler:
    """Simple checkpoint handler for backup progress"""

    def __init__(self):
        self.checkpoints = {}

    def save_checkpoint(self, checkpoint: BackupCheckpoint):
        """Save a checkpoint"""
        self.checkpoints[checkpoint.backup_id] = checkpoint

    def load_checkpoint(self, backup_id: str) -> Optional[BackupCheckpoint]:
        """Load a checkpoint by backup ID"""
        return self.checkpoints.get(backup_id)


class BackupEngine:
    """
    Main backup engine for TNGD system.
    
    Coordinates all backup operations with advanced features:
    - Resource management and monitoring
    - Checkpoint system for recovery
    - Thread pool management
    - Comprehensive error handling
    - Progress tracking and reporting
    """
    
    def __init__(self, dates: Optional[List[datetime]] = None, config_path: Optional[str] = None, resume_mode: bool = False):
        """
        Initialize the backup engine.

        Args:
            dates: List of dates to backup (defaults to today)
            config_path: Path to configuration file
            resume_mode: Whether to resume from where backup left off
        """
        self.dates = dates or [datetime.now()]
        self.backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.resume_mode = resume_mode

        # Setup logging
        self.log_file_path = self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Initialize configuration
        self.config_manager = ConfigManager(config_path)
        self.backup_config = BackupConfig.from_config_manager(self.config_manager)
        
        # Initialize checkpoint system
        self.checkpoint_handler = CheckpointHandler()
        self.checkpoint = BackupCheckpoint(
            backup_id=self.backup_id,
            start_time=datetime.now(),
            dates=[self.dates[0].strftime('%Y-%m-%d')],  # Single backup run timestamp
            tables=[],
            completed_tables={},
            failed_tables={}
        )
        
        # Initialize components
        self.devo_client = None
        self.storage_manager = None
        self.email_service = None
        self.streaming_processor = None

        # Initialize performance optimization
        self.performance_optimizer = None
        self.performance_tracking_enabled = False
        if PERFORMANCE_OPTIMIZATION_AVAILABLE:
            try:
                self.performance_optimizer = PerformanceOptimizer()
                self.performance_tracking_enabled = True
                self.logger.info("Performance optimization enabled")
            except Exception as e:
                self.logger.warning(f"Failed to initialize performance optimizer: {e}")
        else:
            self.logger.info("Performance optimization not available")
        self.resource_monitor = ResourceMonitor()
        
        # Resource management
        # Note: Removed unused thread_manager assignment
        self.temp_files = []
        
        # Performance tracking
        self.start_time = None
        self.metrics = {
            'total_tables': 0,
            'completed_tables': 0,
            'failed_tables': 0,
            'total_rows': 0,
            'total_duration': 0
        }
        
        self.logger.info(f"BackupEngine initialized: {self.backup_id}")





    def _create_table_archive(self, chunk_files: List[str], archive_name: str, output_dir: str) -> str:
        """
        Create a single tar.gz archive containing all chunk files for a table.

        Args:
            chunk_files: List of chunk file paths
            archive_name: Name for the archive file
            output_dir: Directory to create the archive in

        Returns:
            Path to the created archive
        """
        archive_path = os.path.join(output_dir, archive_name)

        try:
            self.logger.info(f"[ARCHIVE] Creating archive {archive_name} with {len(chunk_files)} chunk files")

            with tarfile.open(archive_path, 'w:gz') as tar:
                for i, chunk_file in enumerate(chunk_files, 1):
                    if os.path.exists(chunk_file):
                        # Add file to archive with just the filename (not full path)
                        arcname = os.path.basename(chunk_file)
                        chunk_size_mb = os.path.getsize(chunk_file) / (1024 * 1024)
                        tar.add(chunk_file, arcname=arcname)
                        self.logger.info(f"  [CHUNK] Added chunk {i}/{len(chunk_files)}: {arcname} ({chunk_size_mb:.2f} MB)")
                    else:
                        self.logger.warning(f"Chunk file not found: {chunk_file}")

            # Clean up individual chunk files after archiving
            self.logger.info(f"[CLEANUP] Cleaning up {len(chunk_files)} individual chunk files")
            cleaned_count = 0
            for chunk_file in chunk_files:
                try:
                    if os.path.exists(chunk_file):
                        os.remove(chunk_file)
                        cleaned_count += 1
                        self.logger.debug(f"Cleaned up chunk file: {os.path.basename(chunk_file)}")
                except Exception as cleanup_error:
                    self.logger.warning(f"Failed to clean up chunk file {chunk_file}: {cleanup_error}")

            archive_size = os.path.getsize(archive_path) / (1024 * 1024)  # Size in MB
            self.logger.info(f"[SUCCESS] Archive created successfully: {archive_name} ({archive_size:.2f} MB)")
            self.logger.info(f"[CLEANUP] Cleaned up {cleaned_count}/{len(chunk_files)} chunk files")

            return archive_path

        except Exception as archive_error:
            self.logger.error(f"Failed to create archive {archive_name}: {archive_error}")
            # Clean up partial archive if it exists
            if os.path.exists(archive_path):
                try:
                    os.remove(archive_path)
                except (OSError, PermissionError):
                    # OSError: file doesn't exist or other OS-level error
                    # PermissionError: insufficient permissions to delete file
                    pass
            raise

    def _setup_logging(self) -> str:
        """Setup logging configuration."""
        # Create logs directory in data folder
        logs_dir = Path("data/logs")
        logs_dir.mkdir(parents=True, exist_ok=True)

        # Generate log filename
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        date_info = ""
        if self.dates:
            if len(self.dates) == 1:
                date_info = f"_{self.dates[0].strftime('%Y-%m-%d')}"
            else:
                start_date = self.dates[0].strftime('%Y-%m-%d')
                end_date = self.dates[-1].strftime('%Y-%m-%d')
                date_info = f"_{start_date}_to_{end_date}"

        log_filename = f"tngd_backup_{timestamp}{date_info}.log"
        log_file_path = logs_dir / log_filename

        # Clear any existing handlers to avoid conflicts
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # Configure logging with force=True to override any existing configuration
        # Create a custom StreamHandler that handles encoding errors gracefully

        class SafeStreamHandler(logging.StreamHandler):
            """StreamHandler that handles encoding errors gracefully on Windows."""
            def emit(self, record):
                try:
                    super().emit(record)
                except UnicodeEncodeError:
                    # If Unicode encoding fails, replace problematic characters
                    msg = self.format(record)
                    safe_msg = msg.encode('ascii', errors='replace').decode('ascii')
                    print(safe_msg, file=self.stream)

        console_handler = SafeStreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file_path, encoding='utf-8'),
                console_handler
            ],
            force=True  # This ensures the configuration is applied even if basicConfig was called before
        )

        # Log the setup completion
        logger = logging.getLogger(__name__)
        logger.info(f"Logging configured. Log file: {log_file_path}")

        return str(log_file_path)
    
    def initialize_components(self) -> bool:
        """
        Initialize all backup components.
        
        Returns:
            bool: True if initialization successful
        """
        try:
            self.logger.info("Initializing backup components...")
            
            # Initialize Devo client
            self.devo_client = DevoClient()
            self.logger.info("Devo client initialized")
            
            # Initialize storage manager
            self.storage_manager = StorageManager(self.config_manager)
            self.logger.info("Storage manager initialized")
            
            # Initialize email service
            try:
                self.email_service = EmailService()
                self.logger.info("Email service initialized")
            except Exception as email_init_error:
                self.logger.warning(f"Email service initialization failed: {email_init_error}")
                self.email_service = None
            
            # Initialize streaming processor
            streaming_config = StreamingConfig(
                default_chunk_size=self.backup_config.streaming.default_chunk_size,
                streaming_threshold_rows=self.backup_config.streaming.streaming_threshold_rows,
                memory_threshold_mb=self.backup_config.resource.memory_threshold_mb
            )
            self.streaming_processor = StreamingDataProcessor(streaming_config, self._log_step)
            self.logger.info("Streaming processor initialized")
            
            return True
            
        except Exception as init_error:
            self.logger.error(f"Component initialization failed: {init_error}")
            return False
    
    def load_tables(self, resume_mode: bool = False) -> List[str]:
        """Load table list from configuration based on environment."""
        try:
            # Check for resume mode first
            if resume_mode:
                resume_file = Path("config/tables_resume.json")
                if resume_file.exists():
                    with open(resume_file, 'r') as f:
                        tables = json.load(f)
                    self.logger.info(f"Resume mode: Loaded {len(tables)} remaining tables from {resume_file}")
                    return tables
                else:
                    self.logger.warning("Resume mode requested but no resume file found, using normal mode")

            # Determine which table configuration to use
            config_env = self._get_environment_from_config()

            if config_env == "production":
                # Production mode: load all tables from production table list
                tables_file = Path("config/tables_production.json")
                if tables_file.exists():
                    with open(tables_file, 'r') as f:
                        tables = json.load(f)
                    self.logger.info(f"Production mode: Loaded {len(tables)} tables from {tables_file}")
                    return tables
                else:
                    # Fallback to main tables.json for production
                    tables_file = Path("config/tables.json")
                    if tables_file.exists():
                        with open(tables_file, 'r') as f:
                            tables = json.load(f)
                        self.logger.info(f"Production mode (fallback): Loaded {len(tables)} tables from {tables_file}")
                        return tables
            else:
                # Default mode: use main tables.json
                tables_file = Path("config/tables.json")
                if tables_file.exists():
                    with open(tables_file, 'r') as f:
                        tables = json.load(f)
                    self.logger.info(f"Default mode: Loaded {len(tables)} tables from {tables_file}")
                    return tables

            # Final fallback if no table files found
            fallback_tables = [
                'my.app.tngd.polardb',
                'cloud.office365.management.exchange',
                'firewall.fortinet.traffic.forward'
            ]
            self.logger.warning(f"No table files found, using fallback: {len(fallback_tables)} tables")
            return fallback_tables

        except Exception as table_load_error:
            self.logger.error(f"Failed to load tables: {table_load_error}")
            # Return minimal fallback to prevent complete failure
            return ['my.app.tngd.polardb', 'cloud.office365.management.exchange', 'firewall.fortinet.traffic.forward']

    def _get_environment_from_config(self) -> str:
        """Determine environment from configuration file path."""
        if hasattr(self, 'config_manager') and self.config_manager.config_file:
            config_file = str(self.config_manager.config_file).lower()
            if 'development' in config_file:
                return "development"
            elif 'production' in config_file:
                return "production"
        return "default"
    
    def run_backup(self) -> Dict[str, Any]:
        """
        Run the backup process for specified dates.

        Returns:
            Dict containing backup results and metrics
        """
        self.start_time = time.time()
        self.logger.info("=== TNGD BACKUP ENGINE STARTED ===")
        self.logger.info(f"Backup ID: {self.backup_id}")
        self.logger.info(f"Log file: {self.log_file_path}")

        # Always use date-specific backup
        date_list = [d.strftime('%Y-%m-%d') for d in self.dates]
        self.logger.info(f"Mode: DATE-SPECIFIC BACKUP for dates: {', '.join(date_list)}")

        # Initialize results for complete data backup
        results = {
            'backup_id': self.backup_id,
            'status': 'completed',
            'start_time': self.start_time,
            'backup_run': self.dates[0].strftime('%Y-%m-%d %H:%M:%S'),
            'metrics': self.metrics,
            'table_results': [],  # Store detailed results for each table
            'error': None
        }

        try:
            # Initialize components
            if not self.initialize_components():
                raise Exception("Component initialization failed")

            # Load tables
            tables = self.load_tables(resume_mode=self.resume_mode)
            self.metrics['total_tables'] = len(tables)

            # Start resource monitoring
            self.resource_monitor.start_monitoring()

            # Process tables with date-specific backup
            self.logger.info(f"Processing {len(tables)} tables for date-specific backup")
            table_results = self._backup_tables_by_date(tables)

            # Store table results
            results['table_results'] = table_results

            # Update summary metrics
            for table_result in table_results:
                if table_result.get('status') == 'completed':
                    self.metrics['completed_tables'] += 1
                    self.metrics['total_rows'] += table_result.get('rows', 0)
                else:
                    self.metrics['failed_tables'] += 1

            self.logger.info("=== COMPLETE DATA BACKUP FINISHED ===")

        except Exception as backup_error:
            results['status'] = 'failed'
            results['error'] = str(backup_error)
            self.logger.error(f"Backup failed: {backup_error}")

        finally:
            # Cleanup and finalization
            self._finalize_backup(results)

        return results



    def _backup_tables_by_date(self, tables: List[str]) -> List[Dict[str, Any]]:
        """
        Backup tables with date-specific filtering.

        Args:
            tables: List of table names to backup

        Returns:
            List of table results
        """
        from ..utils.timestamp_helper import create_date_filter

        table_results = []
        backup_run_id = self.dates[0].strftime('%Y%m%d_%H%M%S')

        self.logger.info(f"Starting date-specific backup for {len(tables)} tables")
        self.logger.info(f"Backup Run ID: {backup_run_id}")
        self.logger.info(f"Target dates: {[d.strftime('%Y-%m-%d') for d in self.dates]}")

        # Create output directory for this backup run
        output_dir = Path("data/exports") / backup_run_id
        output_dir.mkdir(parents=True, exist_ok=True)

        for table_index, table in enumerate(tables, 1):
            try:
                # Process each date for this table
                table_total_rows = 0
                table_chunk_files = []
                table_start_time = time.time()

                self.logger.info(f"[PROCESS] Processing table {table_index}/{len(tables)}: {table}")

                # Apply performance optimizations if available
                operation_context = self._prepare_optimization_context(table, table_index, len(tables))
                optimized_settings = self._apply_table_optimizations(operation_context)

                for date_index, target_date in enumerate(self.dates, 1):
                    date_str = target_date.strftime('%Y-%m-%d')
                    self.logger.info(f"[DATE] Processing date {date_index}/{len(self.dates)}: {date_str}")

                    # Create date-specific WHERE clause
                    where_clause = create_date_filter(date_str, simple=True)
                    self.logger.info(f"[QUERY] Using WHERE clause: {where_clause}")

                    # Query table data for this specific date
                    query_result = self.devo_client.query_table_to_file(
                        table_name=table,
                        where_clause=where_clause,
                        output_dir=str(output_dir),
                        chunk_size=BackupConstants.DEFAULT_CHUNK_SIZE,
                        timeout=BackupConstants.DEFAULT_TIMEOUT_SECONDS,
                        max_retries=BackupConstants.DEFAULT_MAX_RETRIES
                    )

                    date_rows = query_result.get('total_rows', 0)
                    date_files = query_result.get('file_paths', [])

                    table_total_rows += date_rows
                    table_chunk_files.extend(date_files)

                    self.logger.info(f"[DATA] Date {date_str}: {date_rows:,} rows in {len(date_files)} files")

                table_duration = time.time() - table_start_time

                if table_total_rows > 0:
                    result = self._process_table_with_data_dated(
                        table, table_total_rows, table_chunk_files, output_dir, backup_run_id, table_duration, optimized_settings
                    )
                else:
                    result = self._process_table_no_data(table, table_duration)
                    # Record performance for no-data case
                    self._record_table_performance(table, table_duration, 0, True, {'status': 'no_data'})

                table_results.append(result)

            except Exception as e:
                table_duration = time.time() - table_start_time if 'table_start_time' in locals() else 0
                self.logger.error(f"Error processing table {table}: {str(e)}")

                result = {
                    'table_name': table,
                    'status': 'failed',
                    'rows': 0,
                    'duration': table_duration,
                    'error': str(e)
                }
                table_results.append(result)

                # Record performance for failed case
                self._record_table_performance(table, table_duration, 0, False, {'error': str(e)})

            # Add separator for readability
            if table_index < len(tables):
                self.logger.info(f"[NEXT] Proceeding to next table...")
                self.logger.info("-" * 80)

        # Summary
        completed = sum(1 for r in table_results if r['status'] == 'completed')
        failed = sum(1 for r in table_results if r['status'] == 'failed')
        no_data = sum(1 for r in table_results if r['status'] == 'no_data')
        total_rows = sum(r['rows'] for r in table_results)

        date_list = [d.strftime('%Y-%m-%d') for d in self.dates]
        self.logger.info(f"Date-specific backup finished for {', '.join(date_list)}: {completed} successful, {failed} failed, {no_data} no data, {total_rows:,} total rows")
        return table_results

    def _process_table_with_data_dated(self, table: str, total_rows: int, chunk_files: List[str],
                                      output_dir: Path, backup_run_id: str, table_duration: float,
                                      optimized_settings: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process a table that has data for date-specific backup."""
        upload_success = True

        self.logger.info(f"[STEP 2] Data pulled successfully - {total_rows:,} rows in {len(chunk_files)} chunk files")

        if chunk_files:
            try:
                # Create a single archive containing all chunk files for this table
                self.logger.info(f"[STEP 3] Creating archive from {len(chunk_files)} chunk files")
                # Include date in filename to match existing structure
                date_str = self.dates[0].strftime('%Y-%m-%d')
                archive_name = f"{table.replace('.', '_')}_{date_str}.tar.gz"
                archive_path = self._create_table_archive(chunk_files, archive_name, str(output_dir))

                # Upload the single archive (use date-specific path)
                self.logger.info(f"[STEP 4] Uploading archive to OSS storage")
                oss_path = self._generate_upload_path_dated(backup_run_id, table, Path(archive_path).name)
                success, upload_details = self.storage_manager.upload_file(archive_path, oss_path)

                if not success:
                    upload_success = False
                    self.logger.error(f"Failed to upload {archive_path}: {upload_details.get('error', 'Unknown error')}")
                    self.temp_files.append(archive_path)
                else:
                    # Show [UPLOAD] log as requested by user
                    archive_size_mb = os.path.getsize(archive_path) / (1024 * 1024)
                    self.logger.info(f"[UPLOAD] {Path(archive_path).name} -> OSS ({archive_size_mb:.2f} MB, {len(chunk_files)} chunks, {total_rows:,} rows)")

                    self.logger.info(f"[STEP 4 COMPLETE] Successfully uploaded {Path(archive_path).name} containing {len(chunk_files)} chunks ({total_rows} rows)")
                    self._cleanup_archive_after_upload(archive_path)

            except Exception as e:
                upload_success = False
                self.logger.error(f"Error creating/uploading archive for table {table}: {str(e)}")

        # Create result
        result = {
            'table_name': table,
            'status': 'completed' if upload_success else 'failed',
            'rows': total_rows,
            'chunks': len(chunk_files),
            'duration': table_duration,
            'upload_success': upload_success
        }

        self.logger.info(f"[COMPLETE] Table completed: {table} ({table_duration:.1f}s)")

        # Record performance metrics
        self._record_table_performance(
            table, table_duration, total_rows, upload_success,
            {'chunks': len(chunk_files), 'optimizations_applied': optimized_settings}
        )

        return result

    def _generate_upload_path_dated(self, backup_run_id: str, table_name: str, filename: str) -> str:
        """Generate upload path for date-specific backup using configured path structure."""
        # Get upload path structure configuration
        path_config = self.backup_config.storage.upload_path_structure
        target_date = self.dates[0]

        # Start with base path (only if not empty)
        path_parts = []
        if path_config.base_path:
            path_parts.append(path_config.base_path)

        # Add provider path
        if path_config.provider_path:
            path_parts.append(path_config.provider_path)

        # Add month folder if enabled
        if path_config.use_month_folders:
            month_name = calendar.month_name[target_date.month]
            path_parts.append(month_name)

        # Add week folder if enabled
        if path_config.use_week_folders:
            # Calculate week number within the month
            week_of_month = ((target_date.day - 1) // 7) + 1
            path_parts.append(f"week {week_of_month}")

        # Add date folder if enabled
        if path_config.use_date_folders:
            date_str = target_date.strftime('%Y-%m-%d')
            path_parts.append(date_str)

        # Add backup run ID folder if enabled (disabled by default to remove timestamp folder)
        if getattr(path_config, 'include_backup_run_id', False):  # Default to False to remove timestamp folder
            path_parts.append(backup_run_id)

        # Add table folder if enabled
        if path_config.include_table_folders:
            safe_table_name = table_name.replace('.', '_')
            path_parts.append(safe_table_name)

        # Add filename
        path_parts.append(filename)

        # Join all parts with forward slashes (OSS uses forward slashes)
        upload_path = '/'.join(path_parts)

        return upload_path

    def _process_table_no_data(self, table: str, table_duration: float) -> Dict[str, Any]:
        """Process a table that has no data."""
        self.logger.info(f"[INFO] No data found for table: {table}")

        result = {
            'table_name': table,
            'status': 'no_data',
            'rows': 0,
            'chunks': 0,
            'duration': table_duration,
            'upload_success': True  # No upload needed
        }

        self.logger.info(f"[TABLE COMPLETE] {table} ({table_duration:.1f}s)")
        return result

    def _verify_oss_upload(self, oss_path: str, expected_size_mb: float) -> bool:
        """
        Verify that a file was successfully uploaded to OSS.

        Args:
            oss_path: The OSS path of the uploaded file
            expected_size_mb: Expected file size in MB

        Returns:
            True if verification passes, False otherwise
        """
        try:
            # Get object info from OSS
            object_info = self.storage_manager.get_object_info(oss_path)

            if not object_info:
                self.logger.error(f"[UPLOAD] Verification failed: File not found in OSS at {oss_path}")
                return False

            # Check file size
            actual_size_mb = object_info['size'] / (1024 * 1024)
            size_difference = abs(actual_size_mb - expected_size_mb)

            if size_difference > 0.1:  # Allow 0.1MB difference for compression variations
                self.logger.warning(f"[UPLOAD] Size mismatch: Expected {expected_size_mb:.2f}MB, got {actual_size_mb:.2f}MB")
                return False

            self.logger.info(f"[UPLOAD] File verified: {actual_size_mb:.2f}MB, Last modified: {object_info['last_modified']}")
            return True

        except Exception as e:
            self.logger.error(f"[UPLOAD] Verification error: {str(e)}")
            return False

    def _cleanup_archive_after_upload(self, archive_path: str):
        """Clean up archive file after successful upload."""
        self.logger.info(f"[STEP 5] Cleaning up temporary data after successful upload")
        try:
            if os.path.exists(archive_path):
                archive_size = os.path.getsize(archive_path) / (1024 * 1024)  # Size in MB
                os.remove(archive_path)
                self.logger.info(f"[STEP 5 COMPLETE] Deleted archive after successful upload: {Path(archive_path).name} ({archive_size:.2f} MB freed)")
            else:
                self.logger.warning(f"Archive file not found for cleanup: {archive_path}")
        except Exception as cleanup_error:
            self.logger.warning(f"Failed to delete archive {archive_path}: {cleanup_error}")
            # Add to temp_files for later cleanup if immediate deletion fails
            self.temp_files.append(archive_path)






    def _log_step(self, step: str, message: str, level: str = "INFO"):
        """Enhanced logging with resource monitoring."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {step}: {message}"
        
        if level == "ERROR":
            self.logger.error(formatted_message)
        elif level == "WARNING":
            self.logger.warning(formatted_message)
        else:
            self.logger.info(formatted_message)
    
    def _finalize_backup(self, results: Dict[str, Any]):
        """Finalize backup process with cleanup and reporting."""
        try:
            # Stop resource monitoring
            self.resource_monitor.stop_monitoring()

            # Calculate final metrics
            if self.start_time:
                total_duration = time.time() - self.start_time
                self.metrics['total_duration'] = total_duration
                # Add fields expected by email service
                results['overall_duration'] = total_duration

            # Add other fields expected by email service (updated for complete backup)
            results['total_dates'] = 1  # Always 1 for complete backup
            results['total_tables'] = self.metrics.get('total_tables', 0)

            # Create date_results structure for email compatibility
            if 'table_results' in results:
                completed = sum(1 for r in results['table_results'] if r['status'] == 'completed')
                failed = sum(1 for r in results['table_results'] if r['status'] == 'failed')
                no_data = sum(1 for r in results['table_results'] if r['status'] == 'no_data')
                total_rows = sum(r['rows'] for r in results['table_results'])

                # Create a single date result for email service compatibility
                date_result = {
                    'date': results['backup_run'].split(' ')[0],  # Extract date part
                    'completed': completed,
                    'failed': failed,
                    'no_data': no_data,
                    'total_rows': total_rows,
                    'table_results': results['table_results']
                }
                results['date_results'] = [date_result]

            # Cleanup temp files
            self._cleanup_temp_files()

            # Send email summary
            if self.email_service:
                try:
                    self.logger.info("Sending backup summary email...")
                    self.logger.debug(f"Email data - Complete backup, Table results: {len(results.get('table_results', []))}")
                    self.email_service.send_backup_summary(results)
                    self.logger.info("Backup summary email sent")
                except Exception as email_error:
                    self.logger.warning(f"Failed to send email: {email_error}")

            # Final resource cleanup
            gc.collect()
            # Thread metrics logging removed to reduce log spam

        except Exception as finalization_error:
            self.logger.error(f"Finalization error: {finalization_error}")
    
    def _cleanup_temp_files(self):
        """Clean up temporary files."""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    self.logger.debug(f"Cleaned up temp file: {temp_file}")
            except Exception as cleanup_error:
                self.logger.warning(f"Failed to cleanup {temp_file}: {cleanup_error}")
        
        self.temp_files.clear()

    def _prepare_optimization_context(self, table_name: str, table_index: int, total_tables: int) -> Dict[str, Any]:
        """Prepare context for performance optimizations."""
        if not self.performance_tracking_enabled:
            return {}

        try:
            # Get current system health
            current_health = self.resource_monitor.get_current_metrics() if self.resource_monitor else None

            # Get historical performance for this table
            operation_name = f"backup_table_{table_name}"
            analysis = self.performance_optimizer.get_performance_analysis(operation_name)

            context = {
                'operation': operation_name,
                'table_name': table_name,
                'table_index': table_index,
                'total_tables': total_tables,
                'current_health': current_health,
                'historical_analysis': analysis.get('operation_analyses', {}).get(operation_name)
            }

            return context

        except Exception as e:
            self.logger.debug(f"Failed to prepare optimization context: {e}")
            return {}

    def _apply_table_optimizations(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply performance optimizations for table backup."""
        if not self.performance_tracking_enabled or not context:
            return {}

        try:
            optimizations = {}

            # Apply smart throttling if needed
            should_throttle, throttle_actions = self.performance_optimizer.should_apply_throttling()
            if should_throttle:
                self.logger.info(f"Applying throttling for {context.get('table_name', 'unknown')}")
                if self.resource_monitor:
                    self.resource_monitor.apply_throttling(context['operation'])
                optimizations['throttling'] = throttle_actions

            return optimizations

        except Exception as e:
            self.logger.debug(f"Failed to apply optimizations: {e}")
            return {}

    def _record_table_performance(self, table_name: str, duration: float,
                                 rows_processed: int, success: bool, metadata: Dict[str, Any] = None):
        """Record performance metrics for table backup."""
        if not self.performance_tracking_enabled:
            return

        try:
            operation_name = f"backup_table_{table_name}"
            throughput = rows_processed / duration if duration > 0 else 0

            self.performance_optimizer.record_operation_performance(
                operation_name, duration, throughput, success, metadata or {}
            )

        except Exception as e:
            self.logger.debug(f"Failed to record performance: {e}")


def main():
    """Main entry point for backup engine."""
    import argparse
    
    parser = argparse.ArgumentParser(description="TNGD Backup Engine")
    parser.add_argument("dates", nargs="*", help="Dates to backup (YYYY-MM-DD format)")
    parser.add_argument("--config", help="Configuration file path")
    
    args = parser.parse_args()
    
    # Parse dates
    dates = []
    if args.dates:
        for date_str in args.dates:
            try:
                dates.append(datetime.strptime(date_str, '%Y-%m-%d'))
            except ValueError:
                print(f"Invalid date format: {date_str}. Use YYYY-MM-DD")
                sys.exit(1)
    
    # Create and run backup engine
    engine = BackupEngine(dates, args.config)
    results = engine.run_backup()
    
    # Exit with appropriate code
    sys.exit(0 if results['status'] == 'completed' else 1)


if __name__ == "__main__":
    main()
