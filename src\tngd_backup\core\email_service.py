#!/usr/bin/env python3
"""
Email Service Module

This module provides email functionality for the TNGD backup system.
It handles SMTP configuration, email formatting, and sending functionality
using credentials from .env file.

Features:
- SMTP email sending with Gmail support
- HTML email templates using Jinja2
- Backup summary report generation
- Error handling and logging
- Mock mode for testing
"""

import os
import smtplib
import logging
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
from typing import Dict, Any
from jinja2 import Template
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)


class EmailServiceError(Exception):
    """Custom exception for email service errors."""
    pass


class EmailService:
    """
    Email service for sending backup summary reports.
    
    This service handles SMTP configuration, email formatting, and sending
    functionality using credentials from environment variables.
    """
    
    def __init__(self):
        """Initialize the email service with configuration from environment."""
        self.smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
        self.smtp_port = int(os.getenv('SMTP_PORT', '587'))
        self.sender_email = os.getenv('SMTP_SENDER')
        self.receiver_email = os.getenv('SMTP_RECEIVER')
        self.smtp_password = os.getenv('SMTP_PASSWORD')
        self.mock_mode = os.getenv('SMTP_MOCK_MODE', 'true').lower() == 'true'
        
        # Validate required configuration
        self._validate_config()
        
        logger.info(f"Email service initialized - Mock mode: {self.mock_mode}")
        if not self.mock_mode:
            logger.info(f"SMTP server: {self.smtp_server}:{self.smtp_port}")
            logger.info(f"Sender: {self.sender_email}")
            logger.info(f"Receiver: {self.receiver_email}")
    
    def _validate_config(self):
        """Validate email configuration."""
        required_fields = ['SMTP_SENDER', 'SMTP_RECEIVER']
        missing_fields = []
        
        for field in required_fields:
            if not os.getenv(field):
                missing_fields.append(field)
        
        if not self.mock_mode and not self.smtp_password:
            missing_fields.append('SMTP_PASSWORD')
        
        if missing_fields:
            raise EmailServiceError(f"Missing required email configuration: {', '.join(missing_fields)}")



    def _create_backup_summary_html(self, backup_results: Dict[str, Any]) -> str:
        """Create HTML content for backup summary email."""
        
        # HTML template for backup summary
        html_template = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; background-color: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); overflow: hidden; }
        .header { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 30px 20px; text-align: center; position: relative; }
        .header::after { content: ''; position: absolute; bottom: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #3498db, #2980b9, #3498db); }
        .status-success { color: #27ae60; font-weight: bold; }
        .status-failed { color: #e74c3c; font-weight: bold; }
        .status-warning { color: #f39c12; font-weight: bold; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 20px; }
        .summary-card { background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); padding: 20px; border-radius: 10px; text-align: center; border: 1px solid #e9ecef; box-shadow: 0 2px 8px rgba(0,0,0,0.08); }
        .summary-card h3 { margin: 0 0 12px 0; color: #495057; font-size: 13px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; }
        .summary-card .number { font-size: 28px; font-weight: 700; margin-bottom: 5px; }
        .number-default { color: #2c3e50; }
        .number-success { color: #27ae60; }
        .number-failed { color: #e74c3c; }
        .number-warning { color: #f39c12; }
        .number-info { color: #3498db; }
        .table-results { margin: 30px 20px; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.08); }
        .table-results h3 { color: #2c3e50; margin: 0 0 20px 0; font-size: 18px; font-weight: 600; padding: 20px 20px 0 20px; }
        .table-results table { width: 100%; border-collapse: collapse; }
        .table-results th, .table-results td { padding: 12px 20px; text-align: left; border-bottom: 1px solid #e9ecef; }
        .table-results th { background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%); color: white; font-weight: 600; font-size: 14px; text-transform: uppercase; letter-spacing: 0.5px; }
        .table-results tr:nth-child(even) { background-color: #f8f9fa; }
        .table-results tr:hover { background-color: #e3f2fd; }
        .footer { margin-top: 40px; padding: 25px 20px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 3px solid #3498db; color: #6c757d; font-size: 12px; text-align: center; }
        .error-details { background-color: #fdf2f2; border: 1px solid #f5c6cb; padding: 15px; border-radius: 6px; margin: 10px 0; }
        .success-icon { color: #27ae60; }
        .error-icon { color: #e74c3c; }
        .warning-icon { color: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Email Content Wrapper -->
        <div class="header">
            <img src="cid:logo" alt="Techlab Security Logo" style="height: 45px; width: auto; margin-bottom: 25px; max-width: 180px;" />
            <h1 style="margin: 0 0 15px 0; font-size: 28px; font-weight: 600; letter-spacing: 0.5px;">TNGD Backup System Report</h1>
            <div style="background-color: rgba(255,255,255,0.1); padding: 15px 25px; border-radius: 8px; margin: 15px auto; max-width: 500px;">
                <p style="margin: 0 0 8px 0; font-size: 14px; opacity: 0.9;">Backup completed on {{ timestamp }}</p>
                {% if date_results %}
                <p style="margin: 0 0 8px 0; font-size: 14px; opacity: 0.9;">
                    Backup Date(s):
                    {% for date_result in date_results %}
                        <span style="background-color: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 4px; margin: 0 2px;">{{ date_result.date }}</span>{% if not loop.last %}, {% endif %}
                    {% endfor %}
                </p>
                {% endif %}
                <p style="margin: 0; font-size: 16px; font-weight: 600;" class="{% if overall_status == 'completed' %}status-success{% else %}status-failed{% endif %}">
                    Status: {{ overall_status.upper() }}
                </p>
            </div>
        </div>

        <!-- Main Content Area -->
        <div style="padding: 0 0 20px 0;">
        <div class="summary-grid">
            <div class="summary-card">
                <h3>Dates Processed</h3>
                <div class="number number-info">{{ total_dates }}</div>
            </div>
            <div class="summary-card">
                <h3>Tables Processed</h3>
                <div class="number number-info">{{ total_tables }}</div>
            </div>
            <div class="summary-card">
                <h3>Successful</h3>
                <div class="number number-success">{{ total_successful }}</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="number number-failed">{{ total_failed }}</div>
            </div>
            <div class="summary-card">
                <h3>No Data</h3>
                <div class="number number-warning">{{ total_no_data }}</div>
            </div>
            <div class="summary-card">
                <h3>Total Rows</h3>
                <div class="number number-default">{{ "{:,}".format(total_rows) }}</div>
            </div>
            <div class="summary-card">
                <h3>Duration</h3>
                <div class="number number-default">{{ duration_minutes }}m</div>
            </div>
        </div>
        
        {% if date_results %}
        <div class="table-results">
            <h2>Detailed Results by Date</h2>
            {% for date_result in date_results %}
            <h3>Date: {{ date_result.date }}</h3>
            <table>
                <thead>
                    <tr>
                        <th>Table Name</th>
                        <th>Status</th>
                        <th>Rows</th>
                        <th>Duration</th>
                        <th>Error</th>
                    </tr>
                </thead>
                <tbody>
                    {% for table_result in date_result.table_results %}
                    <tr>
                        <td>{{ table_result.table_name }}</td>
                        <td>
                            {% if table_result.status == 'completed' %}
                                <span class="status-success">Completed</span>
                            {% elif table_result.status == 'no_data' %}
                                <span class="status-warning">No Data</span>
                            {% else %}
                                <span class="status-failed">Failed</span>
                            {% endif %}
                        </td>
                        <td>{{ "{:,}".format(table_result.rows) }}</td>
                        <td>{{ "%.1f"|format(table_result.duration) }}s</td>
                        <td>{{ table_result.error or '-' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endfor %}
        </div>
        {% endif %}
        
        {% if errors %}
        <div class="error-details">
            <h3>Error Details</h3>
            {% for error in errors %}
            <p><strong>{{ error.table }}:</strong> {{ error.message }}</p>
            {% endfor %}
        </div>
        {% endif %}
        </div>
        <!-- End Main Content Area -->

        <div class="footer">
            <p style="margin: 0 0 8px 0; font-weight: 600;">This report was generated automatically by Techlab Automation Team.</p>
            <p style="margin: 0; opacity: 0.8;">Report generated at: {{ timestamp }}</p>
        </div>
    </div>
</body>
</html>
        """
        
        # Create Jinja2 template
        template = Template(html_template)
        
        # Prepare template data
        template_data = self._prepare_template_data(backup_results)
        
        # Render HTML
        return template.render(**template_data)
    
    def _prepare_template_data(self, backup_results: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare data for email template rendering."""

        # Calculate summary statistics
        total_successful = 0
        total_failed = 0
        total_no_data = 0
        total_rows = 0
        errors = []

        date_results = backup_results.get('date_results', [])

        # Verify row counts to detect potential chunk size issues
        self._verify_row_counts(date_results)
        
        for date_result in date_results:
            total_successful += date_result.get('completed', 0)
            total_failed += date_result.get('failed', 0)
            total_no_data += date_result.get('no_data', 0)
            total_rows += date_result.get('total_rows', 0)

            # Collect errors and verify row counts
            for table_result in date_result.get('table_results', []):
                if table_result.get('error'):
                    errors.append({
                        'table': table_result.get('table_name', 'Unknown'),
                        'message': table_result.get('error', 'Unknown error')
                    })

                # Log row count verification for debugging
                table_name = table_result.get('table_name', 'Unknown')
                row_count = table_result.get('rows', 0)
                logger.debug(f"[EMAIL_VERIFICATION] Table {table_name}: {row_count:,} rows")
        
        return {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'overall_status': backup_results.get('status', 'unknown'),
            'total_dates': backup_results.get('total_dates', 0),
            'total_tables': backup_results.get('total_tables', 0),
            'total_successful': total_successful,
            'total_failed': total_failed,
            'total_no_data': total_no_data,
            'total_rows': total_rows,
            'duration_minutes': round(backup_results.get('overall_duration', 0) / 60, 1),
            'date_results': date_results,
            'errors': errors[:10]  # Limit to first 10 errors
        }

    def _create_email_subject(self, backup_results: Dict[str, Any]) -> str:
        """Create email subject line based on backup results."""
        status = backup_results.get('status', 'unknown')
        total_dates = backup_results.get('total_dates', 0)

        if status == 'completed':
            # Check if there were any failures
            total_failed = sum(date_result.get('failed', 0) for date_result in backup_results.get('date_results', []))
            if total_failed > 0:
                return f"WARNING: TNGD Backup Completed with {total_failed} Failures - {total_dates} Date(s)"
            else:
                return f"SUCCESS: TNGD Backup Completed Successfully - {total_dates} Date(s)"
        elif status == 'connection_failed':
            return f"ERROR: TNGD Backup Failed - Connection Error"
        else:
            return f"ERROR: TNGD Backup Failed - {total_dates} Date(s)"

    def send_backup_summary(self, backup_results: Dict[str, Any]) -> bool:
        """
        Send backup summary email.

        Args:
            backup_results: Dictionary containing backup results and statistics

        Returns:
            True if email was sent successfully, False otherwise
        """
        try:
            logger.info("Preparing backup summary email...")

            # Create email content
            subject = self._create_email_subject(backup_results)
            html_content = self._create_backup_summary_html(backup_results)

            # Create plain text version (simplified)
            plain_text = self._create_plain_text_summary(backup_results)

            # Send email
            success = self._send_email(subject, html_content, plain_text)

            if success:
                logger.info("Backup summary email sent successfully")
            else:
                logger.error("Failed to send backup summary email")

            return success

        except Exception as e:
            logger.error(f"Error sending backup summary email: {str(e)}")
            return False

    def _create_plain_text_summary(self, backup_results: Dict[str, Any]) -> str:
        """Create plain text version of backup summary."""

        # Calculate summary statistics
        total_successful = 0
        total_failed = 0
        total_no_data = 0
        total_rows = 0

        date_results = backup_results.get('date_results', [])

        for date_result in date_results:
            total_successful += date_result.get('completed', 0)
            total_failed += date_result.get('failed', 0)
            total_no_data += date_result.get('no_data', 0)
            total_rows += date_result.get('total_rows', 0)

        duration_minutes = round(backup_results.get('overall_duration', 0) / 60, 1)

        text = f"""
TNGD Backup System Report
========================

Backup completed on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Status: {backup_results.get('status', 'unknown').upper()}

Summary:
--------
Dates Processed: {backup_results.get('total_dates', 0)}
Tables Processed: {backup_results.get('total_tables', 0)}
Successful: {total_successful}
Failed: {total_failed}
No Data: {total_no_data}
Total Rows: {total_rows:,}
Duration: {duration_minutes} minutes

This report was generated automatically by Techlab Automation Team.
        """

        return text.strip()

    def _verify_row_counts(self, date_results: List[Dict[str, Any]]):
        """
        Verify row counts to detect potential issues with chunk sizes being reported instead of actual rows.

        Args:
            date_results: List of date results from backup
        """
        from ..constants import BackupConstants

        suspicious_counts = []

        for date_result in date_results:
            for table_result in date_result.get('table_results', []):
                table_name = table_result.get('table_name', 'Unknown')
                row_count = table_result.get('rows', 0)

                # Check if row count matches common chunk sizes (potential issue)
                if row_count in [BackupConstants.DEFAULT_CHUNK_SIZE, 50000, 100000, 500000]:
                    suspicious_counts.append({
                        'table': table_name,
                        'rows': row_count,
                        'reason': f'Row count ({row_count:,}) matches common chunk size'
                    })

                # Check for exactly 50,000 rows (the reported issue)
                if row_count == 50000:
                    logger.warning(f"[ROW_COUNT_VERIFICATION] Table {table_name} has exactly 50,000 rows - "
                                 f"verify this is actual data count, not chunk size")

        if suspicious_counts:
            logger.warning(f"[ROW_COUNT_VERIFICATION] Found {len(suspicious_counts)} tables with suspicious row counts:")
            for item in suspicious_counts:
                logger.warning(f"  - {item['table']}: {item['rows']:,} rows ({item['reason']})")
            logger.warning("Please verify these are actual row counts, not chunk size estimates")

    def _send_email(self, subject: str, html_content: str, plain_text: str) -> bool:
        """
        Send email using SMTP.

        Args:
            subject: Email subject
            html_content: HTML email content
            plain_text: Plain text email content

        Returns:
            True if email was sent successfully, False otherwise
        """
        if self.mock_mode:
            logger.info("MOCK MODE: Email would be sent with following details:")
            logger.info(f"  From: {self.sender_email}")
            logger.info(f"  To: {self.receiver_email}")
            logger.info(f"  Subject: {subject}")
            logger.info(f"  Content length: {len(html_content)} characters")
            return True

        # Validate required fields
        if not self.sender_email or not self.receiver_email or not self.smtp_password:
            logger.error("Missing required email configuration for sending")
            return False

        try:
            # Create message with related content for embedded images
            msg = MIMEMultipart('related')
            msg['From'] = self.sender_email
            msg['To'] = self.receiver_email
            msg['Subject'] = subject

            # Create alternative container for text and HTML
            msg_alternative = MIMEMultipart('alternative')

            # Add plain text and HTML parts
            part1 = MIMEText(plain_text, 'plain')
            part2 = MIMEText(html_content, 'html')

            msg_alternative.attach(part1)
            msg_alternative.attach(part2)
            msg.attach(msg_alternative)

            # Attach logo image with Content-ID
            try:
                logo_path = Path("icon.png")
                if logo_path.exists():
                    with open(logo_path, "rb") as f:
                        logo_data = f.read()

                    logo_image = MIMEImage(logo_data)
                    logo_image.add_header('Content-ID', '<logo>')
                    logo_image.add_header('Content-Disposition', 'inline', filename='logo.png')
                    msg.attach(logo_image)
                    logger.info("Logo image attached to email")
                else:
                    logger.warning("Logo file icon.png not found, email will be sent without logo")
            except Exception as e:
                logger.warning(f"Failed to attach logo: {e}")

            # Send email
            logger.info(f"Connecting to SMTP server: {self.smtp_server}:{self.smtp_port}")

            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.sender_email, self.smtp_password)
                server.send_message(msg)

            logger.info("Email sent successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            return False

    def test_email_connection(self) -> bool:
        """
        Test email connection and configuration.

        Returns:
            True if connection test is successful, False otherwise
        """
        if self.mock_mode:
            logger.info("MOCK MODE: Email connection test skipped")
            return True

        # Validate required fields
        if not self.sender_email or not self.smtp_password:
            logger.error("Missing required email configuration for connection test")
            return False

        try:
            logger.info("Testing email connection...")

            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.sender_email, self.smtp_password)

            logger.info("Email connection test successful")
            return True

        except Exception as e:
            logger.error(f"Email connection test failed: {str(e)}")
            return False

    def send_test_email(self) -> bool:
        """
        Send a test email to verify email functionality.

        Returns:
            True if test email was sent successfully, False otherwise
        """
        test_results = {
            'status': 'completed',
            'total_dates': 1,
            'total_tables': 2,
            'overall_duration': 120,
            'date_results': [{
                'date': '2025-06-26',
                'completed': 2,
                'failed': 0,
                'no_data': 0,
                'total_rows': 1500,
                'table_results': [
                    {'table_name': 'test.table.1', 'status': 'completed', 'rows': 750, 'duration': 45.2, 'error': None},
                    {'table_name': 'test.table.2', 'status': 'completed', 'rows': 750, 'duration': 52.8, 'error': None}
                ]
            }]
        }

        logger.info("Sending test email...")
        return self.send_backup_summary(test_results)
